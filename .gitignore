# Node modules
node_modules/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.*.local

# OS-specific files
.DS_Store
Thumbs.db

# Build output
dist/
build/

# IDEs and editors
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln

# Coverage directory used by testing tools
coverage/

# Optional npm cache directory
.npm/

# MongoDB dump files
dump/

# Uploads (if saving files locally)
public/uploads/
uploads/

# Compiled source
*.tsbuildinfo

# Misc
*.tgz
package-lock.json
pnpm-lock.yaml
yarn.lock
