const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const CustomerSchema = new Schema({
    CustomerName:{
        type:String,
        required:true
    },
    Email:{
        type:String,
        required:false,
        unique:false
    },
    PhoneNumber:{
        type:String,
        required:true,
        unique:true
    },
    NicNumber:{
        type:String,
        required:true,
        unique:true
    },
    Address:{
        type:String,
        required:true
    },
    FlatOrRoom:{
        type: mongoose.Schema.ObjectId,
        ref: 'Flat',
        required:true
    },
    AssignedBy:{
        type: mongoose.Schema.ObjectId,
        ref: 'Admin',
        required:true
    },
    AssignedDate:{
        type:Date,
        required:true
    },
    AgreementImage:{
        type:String,
        required:false
    },
    LeaveDate:{
        type:Date,
        required:false
    },
    IsActive:{
        type:Boolean,
        required:false
    }
    
},{timestamps:true});
module.exports = mongoose.model('Customer',CustomerSchema);