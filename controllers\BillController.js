const Bill = require('../models/Billing.model.js');

// Create Bill
const createBill = async (req, res) => {
  try {
    const {
      Customer,
      FlatOrRoom,
      PaymentDate,
      PaymentAmount,
      ElectricityBill,
      WaterBill,
      GasBill,
      IsActive,
    } = req.body;

    if (!Customer || !FlatOrRoom || !PaymentDate || !PaymentAmount) {
      return res.status(400).json({ message: 'Required fields are missing' });
    }
    if(PaymentAmount && PaymentAmount < 0) return res.status(400).json({ message: "PaymentAmount must be greater than or equal to 0" });

    const bill = new Bill({
      UserId: req.user._id,
      Customer,
      FlatOrRoom,
      PaymentDate,
      PaymentAmount,
      ElectricityBill,
      WaterBill,
      GasBill,
      IsActive,
    });

    await bill.save();
    res.status(201).json({ message: 'Bill created successfully', data: bill });
  } catch (error) {
    res.status(500).json({ message: 'Server Error', error: error.message });
  }
};

// Get All Bills
const getBills = async (req, res) => {
  try {
    const bills = await Bill.find({ UserId: req.user._id }).populate('Customer FlatOrRoom');
    res.status(200).json(bills);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching bills', error: error.message });
  }
};

// Update Bill
const updateBill = async (req, res) => {
  try {
    const bill = await Bill.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
    });

    if (!bill) return res.status(404).json({ message: 'Bill not found' });

    res.status(200).json({ message: 'Bill updated successfully', data: bill });
  } catch (error) {
    res.status(500).json({ message: 'Error updating bill', error: error.message });
  }
};

// Delete Bill
const deleteBill = async (req, res) => {
  try {
    const bill = await Bill.findByIdAndDelete(req.params.id);
    if (!bill) return res.status(404).json({ message: 'Bill not found' });

    res.status(200).json({ message: 'Bill deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Error deleting bill', error: error.message });
  }
};


module.exports = { createBill, getBills, updateBill, deleteBill };