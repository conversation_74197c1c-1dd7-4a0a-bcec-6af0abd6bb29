{"name": "flatsmanagementsystem", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon server.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "build": "^0.1.4", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.0", "nodemailer": "^7.0.5", "nodemon": "^3.1.10", "swagger-jsdoc": "^6.2.8"}}