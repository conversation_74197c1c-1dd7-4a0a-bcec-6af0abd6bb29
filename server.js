const express = require('express');
const app = express();
const cors = require('cors')
require('dotenv').config({quiet:true});
const connectDB = require('./config/db');
const AuthRoute = require('./auth/Auth.route');
const FlatRoute = require('./routes/Flat.route');
const CustomerRoute = require('./routes/Customer.route');
const BillRoute = require('./routes/Bill.route');

connectDB();
app.use(cors())
app.use(express.json())

const swaggerSpec=require('./appSwagger');
const swaggerHtml = require('./swaggerCustomUI');

app.get('/swagger.json', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.send(swaggerSpec);
});

app.get('/api-docs', (req, res) => {
  res.setHeader('Content-Type', 'text/html');
  res.send(swaggerHtml);
});


app.get('/test-swagger', (req, res) => {
  try {
    res.json({
      message: 'Swagger spec generated successfully',


      hasSpec: !!swaggerSpec,
      specKeys: Object.keys(swaggerSpec || {}),
      pathsCount: swaggerSpec?.paths ? Object.keys(swaggerSpec.paths).length : 0
    });
  } catch (error) {
    res.status(500).json({
      message: 'Error generating Swagger spec',
      error: error.message
    });
  }
});

app.use('/api/v1/auth',AuthRoute)
app.use('/api/v1/flats',FlatRoute)
app.use('/api/v1/customers',CustomerRoute)
app.use('/api/v1/bills',BillRoute)


if (process.env.NODE_ENV !== 'production') {
const PORT = process.env.PORT || 6166;
  app.listen(PORT, () => {
    console.log(`Server is running: http://localhost:${PORT}`);
  });
}