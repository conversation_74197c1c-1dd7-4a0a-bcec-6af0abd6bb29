const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const BillingSchema = new Schema({
    UserId : {
        type : mongoose.Schema.ObjectId,
        ref : 'Admin',
        required : true
    },
    Customer:{
        type: mongoose.Schema.ObjectId,
        ref: 'Customer',
        required:true
    },
    FlatOrRoom:{
        type: mongoose.Schema.ObjectId,
        ref: 'Flat',
        required:true
    },
    PaymentDate:{
        type:Date,
        required:true
    },
    PaymentAmount:{
        type:Number,
        required:true
    },
    ElectricityBill:{
        type: Number,
        required:false
    },
    WaterBill:{
        type: Number,
        required:false
    },
    GasBill:{
        type: Number,
        required:false
    },
    IsActive:{
        type:Boolean,
        required:true
    }
},{timestamps:true});
module.exports = mongoose.model('Billing', BillingSchema);