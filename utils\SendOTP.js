const nodemailer = require('nodemailer');

const SendOtpEmail =  async (email,otp)=>{
    const transporter = nodemailer.createTransport({
        service: 'gmail',
        auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS
        }
    });
    const mailOptions = {
        from: process.env.SMTP_USER,
        to: email,
        subject: 'OTP for verification',
        text: `Your OTP is ${otp}`
    };
    await transporter.sendMail(mailOptions);
}
module.exports = SendOtpEmail;