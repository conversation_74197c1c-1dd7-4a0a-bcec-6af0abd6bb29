const {RegisterAdmin,Login}=require('../controllers/AuthController');
const authMiddleware = require('../Middleware/Auth.Middleware');
const express = require('express');
const router = express.Router();


/**
 * @swagger
 * /api/v1/auth/Register:
 *   post:
 *     summary: Register a new admin
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - FullName
 *               - Email
 *               - Password
 *               - ConfirmPassword
 *               - PhoneNumber
 *               - Address
 *               - ProfileImage
 *             properties:
 *               FullName:
 *                 type: string
 *                 description: Full name of the admin
 *               Email:
 *                 type: string
 *                 format: email
 *                 description: Email address of the admin
 *               Password:
 *                 type: string
 *                 format: password
 *                 minLength: 6
 *                 description: Password (min 6 characters)
 *               ConfirmPassword:
 *                 type: string
 *                 format: password
 *                 description: Must match Password
 *               PhoneNumber:
 *                 type: string
 *                 description: Phone number starting with '0' (11 digits) or '+' (13 digits)
 *               Address:
 *                 type: string
 *                 description: Physical address
 *               ProfileImage:
 *                 type: string
 *                 description: URL or base64 encoded image
 *     responses:
 *       200:
 *         description: Admin created successfully
 *         content:
 *           application/json:
 *             schema:
 *               properties:
 *                 status:
 *                   type: number
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Admin created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                       example: 5f8d0d55b54764421b7156c3
 *                     FullName:
 *                       type: string
 *                       example: "John Doe"
 *                     Email:
 *                       type: string
 *                       format: email
 *                       example: "<EMAIL>"
 *                     PhoneNumber:
 *                       type: string
 *                       example: "+923001234567"
 *                     Address:
 *                       type: string
 *                       example: "123 Main St, City"
 *                     ProfileImage:
 *                       type: string
 *                       example: "https://example.com/profile.jpg"
 *       400:
 *         description: Bad Request - Validation error
 *         content:
 *           application/json:
 *             schema:
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Passwords do not match"
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.post('/Register',authMiddleware, RegisterAdmin);

/**
 * @swagger
 * /api/v1/auth/Login:
 *   post:
 *     summary: Authenticate an admin user
 *     description: Validates admin credentials and returns a JWT token upon successful authentication
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - Email
 *               - Password
 *             properties:
 *               Email:
 *                 type: string
 *                 format: email
 *                 description: Admin's email address
 *                 example: <EMAIL>
 *               Password:
 *                 type: string
 *                 format: password
 *                 description: Admin's password (min 6 characters)
 *                 example: yourpassword123
 *     responses:
 *       200:
 *         description: Successful login
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: Admin logged in successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: 507f1f77bcf86cd799439011
 *                     FullName:
 *                       type: string
 *                       example: John Doe
 *                     Email:
 *                       type: string
 *                       format: email
 *                       example: <EMAIL>
 *                     token:
 *                       type: string
 *                       example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *       400:
 *         description: Bad Request - Invalid input or credentials
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Password is invalid"
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.post('/Login', Login);

module.exports = router;