name: Auto Merge atif-Eb into Main

on:
  push:
    branches:
      - atif-Eb

jobs:
  merge:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Needed to fetch all branches

      - name: Set up Git
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"

      - name: Fetch all branches
        run: git fetch --all

      - name: Checkout main branch
        run: git checkout main

      - name: Merge atif-Eb into main
        run: |
          git merge origin/atif-Eb --allow-unrelated-histories --no-ff -m "Auto-merged atif-Eb into main"
          git push origin main
