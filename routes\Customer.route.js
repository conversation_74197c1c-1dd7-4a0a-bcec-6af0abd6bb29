const express = require('express');
const router = express.Router();
const authMiddleware = require('../Middleware/Auth.Middleware');
const allowRoles = require('../Middleware/Role.Middleware');
const {
  createCustomer,
  getAllCustomers,
  getCustomerById,
  updateCustomer,
  deleteCustomer
} = require('../controllers/CustomerController');

/**
 * @swagger
 * components:
 *   schemas:
 *     Customer:
 *       type: object
 *       required:
 *         - CustomerName
 *         - PhoneNumber
 *         - NicNumber
 *         - Address
 *         - FlatOrRoom
 *         - AssignedBy
 *         - AssignedDate
 *       properties:
 *         CustomerName:
 *           type: string
 *           description: Full name of the customer
 *         Email:
 *           type: string
 *           format: email
 *           description: Email address of the customer
 *         PhoneNumber:
 *           type: string
 *           description: Contact number of the customer (must be unique)
 *         NicNumber:
 *           type: string
 *           description: National Identity Card number (must be unique)
 *         Address:
 *           type: string
 *           description: Complete address of the customer
 *         FlatOrRoom:
 *           type: string
 *           format: ObjectId
 *           description: Reference to the assigned flat/room
 *         AssignedBy:
 *           type: string
 *           format: ObjectId
 *           description: Admin who assigned the customer
 *         AssignedDate:
 *           type: string
 *           format: date-time
 *           description: Date when customer was assigned
 *         AgreementImage:
 *           type: string
 *           description: URL to the agreement document/image
 *         LeaveDate:
 *           type: string
 *           format: date-time
 *           description: Date when customer left the property
 *         IsActive:
 *           type: boolean
 *           description: Status of the customer (active/inactive)
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 */

/**
 * @swagger
 * /api/v1/customers/add:
 *   post:
 *     summary: Create a new customer
 *     tags: [Customer]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Customer'
 *     responses:
 *       201:
 *         description: Customer created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Customer'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 */
router.post('/add', authMiddleware, allowRoles(['Admin', 'SuperAdmin']), createCustomer);

/**
 * @swagger
 * /api/v1/customers/all:
 *   get:
 *     summary: Get all customers
 *     tags: [Customer]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of customers
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Customer'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 */
router.get('/all', authMiddleware, allowRoles(['Admin', 'SuperAdmin']), getAllCustomers);

/**
 * @swagger
 * /api/v1/customers/getall/{id}:
 *   get:
 *     summary: Get customer by ID
 *     tags: [Customer]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Customer ID
 *     responses:
 *       200:
 *         description: Customer details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Customer'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: Customer not found
 */
router.get('/getall:id', authMiddleware, allowRoles(['Admin', 'SuperAdmin']), getCustomerById);

/**
 * @swagger
 * /api/v1/customers/update/{id}:
 *   put:
 *     summary: Update a customer
 *     tags: [Customer]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Customer ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Customer'
 *     responses:
 *       200:
 *         description: Customer updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Customer'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: Customer not found
 */
router.put('/update:id', authMiddleware, allowRoles(['Admin', 'SuperAdmin']), updateCustomer);

/**
 * @swagger
 * /api/v1/customers/delete/{id}:
 *   delete:
 *     summary: Delete a customer
 *     tags: [Customer]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Customer ID
 *     responses:
 *       200:
 *         description: Customer deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: Customer not found
 */
router.delete('/delete/:id', authMiddleware, allowRoles(['Admin', 'SuperAdmin']), deleteCustomer);

module.exports = router;
