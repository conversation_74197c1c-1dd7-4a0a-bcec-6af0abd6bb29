const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const FlatSchema = new Schema({
    UserId : {
        type : mongoose.Schema.ObjectId,
        ref : 'Admin',
        required : true
    },
    FlatOrRoom : {
        type : Number,
        required : true,
        unique : true
    },
    Floor:{
        type : Number,
        required : false,
    },
    Block:{
        type : String,
        required : false
    },
    TotalArea :{
        type : Number,
        required : false
    },
    AttachedWashroom :{
        type : Boolean,
        required : false
    },
    AttachedKitchen :{
        type : Boolean,
        required : false
    },
    LiftAvailable:{
        type : Boolean,
        required : false
    },
    IsActive : {
        type : Boolean,
        required : true,
        default : true
    },

},{timestamps:true})

module.exports = mongoose.model('Flat',FlatSchema)