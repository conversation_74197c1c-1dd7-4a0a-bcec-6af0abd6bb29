const express = require('express');
const router = express.Router();
const authMiddleware = require('../Middleware/Auth.Middleware');
const allowRoles = require('../Middleware/Role.Middleware');
const {
  createBill,
  getBills,
  updateBill,
  deleteBill,
} = require('../controllers/BillController');

/**
 * @swagger
 * components:
 *   schemas:
 *     Bill:
 *       type: object
 *       required:
 *         - UserId
 *         - Customer
 *         - FlatOrRoom
 *         - PaymentDate
 *         - PaymentAmount
 *         - IsActive
 *       properties:
 *         UserId:
 *           type: string
 *           description: ID of the admin user creating the bill
 *         Customer:
 *           type: string
 *           description: ID of the customer being billed
 *         FlatOrRoom:
 *           type: string
 *           description: ID of the flat or room being billed
 *         PaymentDate:
 *           type: string
 *           format: date
 *           description: Date when the payment is due
 *         PaymentAmount:
 *           type: number
 *           description: Total amount of the bill
 *         ElectricityBill:
 *           type: number
 *           description: Electricity bill amount (optional)
 *         WaterBill:
 *           type: number
 *           description: Water bill amount (optional)
 *         GasBill:
 *           type: number
 *           description: Gas bill amount (optional)
 *         IsActive:
 *           type: boolean
 *           description: Whether the bill is active
 */

/**
 * @swagger
 * /api/v1/bills/create:
 *   post:
 *     summary: Create a new bill
 *     tags: [Bills]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Bill'
 *     responses:
 *       201:
 *         description: Bill created successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 */
router.post('/create', authMiddleware,allowRoles(['Admin', 'SuperAdmin']), createBill);
/**
 * @swagger
 * /api/v1/bills:
 *   get:
 *     summary: Get all bills
 *     tags: [Bills]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of all bills
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Bill'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 */
router.get('/', authMiddleware,allowRoles(['Admin', 'SuperAdmin']), getBills);

/**
 * @swagger
 * /api/v1/bills/{id}:
 *   put:
 *     summary: Update a bill
 *     tags: [Bills]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Bill ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Bill'
 *     responses:
 *       200:
 *         description: Bill updated successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: Bill not found
 */
router.put('/:id', authMiddleware,allowRoles(['Admin', 'SuperAdmin']), updateBill);

/**
 * @swagger
 * /api/v1/bills/{id}:
 *   delete:
 *     summary: Delete a bill
 *     tags: [Bills]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Bill ID
 *     responses:
 *       200:
 *         description: Bill deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: Bill not found
 */
router.delete('/:id', authMiddleware,allowRoles(['Admin', 'SuperAdmin']), deleteBill);

module.exports = router;
