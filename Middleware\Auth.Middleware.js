
const jwt = require('jsonwebtoken');
const Admin = require('../models/Admin.model.js');

const authMiddleware = async (req, res, next) => {
  const authHeader = req.headers.authorization || req.headers.authorization?.split(" ")[1];

  if (!authHeader) {
    return res.status(401).json({ message: 'Unauthorized: No token provided' });
  }

  try {
    const decoded = jwt.verify(authHeader, process.env.JWT_SECRET);
    const user = await Admin.findById(decoded.id);
    if (!user) {
      return res.status(401).json({ message: 'Unauthorized'});
    }
    req.user = user;
    next();
  } catch (error) {
    console.error('Auth Middleware Error:', error);
    return res.status(401).json({ message: 'Unauthorized: Invalid token' });
  }
};

module.exports = authMiddleware;
