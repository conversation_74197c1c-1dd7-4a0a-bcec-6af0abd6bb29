const { CreateFlat, getAllFlats, getFlatById, updateFlat, deleteFlat } = require('../controllers/FlatController');
const allowRoles = require('../Middleware/Role.Middleware');
const authMiddleware = require('../Middleware/Auth.Middleware');
const express = require('express');
const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Flat:
 *       type: object
 *       required:
 *         - UserId
 *         - FlatOrRoom
 *         - IsActive
 *       properties:
 *         UserId:
 *           type: string
 *           format: ObjectId
 *           description: Reference to the admin who created the flat
 *         FlatOrRoom:
 *           type: number
 *           description: Flat/Room number (must be unique)
 *         Floor:
 *           type: number
 *           description: Floor number where the flat is located
 *         Block:
 *           type: string
 *           description: Block or building identifier
 *         TotalArea:
 *           type: number
 *           description: Total area of the flat in square feet/meters
 *         AttachedWashroom:
 *           type: boolean
 *           description: Whether the flat has an attached washroom
 *         AttachedKitchen:
 *           type: boolean
 *           description: Whether the flat has an attached kitchen
 *         LiftAvailable:
 *           type: boolean
 *           description: Whether lift is available to the flat
 *         IsActive:
 *           type: boolean
 *           description: Whether the flat is currently active/available
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 */

/**
 * @swagger
 * /api/v1/flats/CreateFlat:
 *   post:
 *     summary: Create a new flat/room
 *     tags: [Flat]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Flat'
 *     responses:
 *       201:
 *         description: Flat created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Flat'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 */
router.post('/CreateFlat', authMiddleware, allowRoles(['Admin', 'SuperAdmin']), CreateFlat);

/**
 * @swagger
 * /api/v1/flats/getAllFlats:
 *   get:
 *     summary: Get all flats/rooms
 *     tags: [Flat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *     responses:
 *       200:
 *         description: List of all flats/rooms
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Flat'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 */
router.get('/getAllFlats', authMiddleware, allowRoles(['Admin', 'SuperAdmin']), getAllFlats);

/**
 * @swagger
 * /api/v1/flats/getFlatById/{id}:
 *   get:
 *     summary: Get flat/room by ID
 *     tags: [Flat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Flat ID
 *     responses:
 *       200:
 *         description: Flat details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Flat'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: Flat not found
 */
router.get('/getFlatById/:id', authMiddleware, allowRoles(['Admin', 'SuperAdmin']), getFlatById);

/**
 * @swagger
 * /api/v1/flats/updateFlat/{id}:
 *   put:
 *     summary: Update a flat/room
 *     tags: [Flat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Flat ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Flat'
 *     responses:
 *       200:
 *         description: Flat updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Flat'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: Flat not found
 */
router.put('/updateFlat/:id', authMiddleware, allowRoles(['Admin', 'SuperAdmin']), updateFlat);

/**
 * @swagger
 * /api/v1/flats/deleteFlat/{id}:
 *   delete:
 *     summary: Delete a flat/room
 *     tags: [Flat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Flat ID
 *     responses:
 *       200:
 *         description: Flat deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: Flat not found
 */
router.delete('/deleteFlat/:id', authMiddleware, allowRoles(['Admin', 'SuperAdmin']), deleteFlat);

module.exports = router;