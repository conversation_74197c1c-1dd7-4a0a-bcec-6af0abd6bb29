const Flat = require('../models/Flat.model.js');
const CreateFlat = async (req, res) => {
    const {
        FlatOrRoom,
        Floor,
        Block,
        TotalArea,
        AttachedWashroom,
        AttachedKitchen,
        ProfileImage,
        LiftAvailable,
        IsActive
    } = req.body;
    if (!FlatOrRoom || FlatOrRoom < 0) {
        return res.status(400).json({ message: "FlatOrRoom is required" });
    }
    if (!Floor || Floor < 0) {
        return res.status(400).json({ message: "Floor is required" });
    }
    if (TotalArea && TotalArea < 0) {
        return res.status(400).json({ message: "TotalArea is greater than 0" });
    }
    const findFlat = await Flat.findOne({ FlatOrRoom: FlatOrRoom, Floor: Floor });
    if (findFlat) {
        return res.status(400).json({ message: "Flat already exists" });
    }
    const newFlat = new Flat({
        UserId: req.user._id,
        FlatOrRoom,
        Floor,
        Block,
        TotalArea,
        AttachedWashroom,
        AttachedKitchen,
        ProfileImage,
        LiftAvailable,
        IsActive
    });

    try {
        const savedFlat = await newFlat.save();
        res.status(201).json({ status: 201, message: 'Flat created successfully', Flat: savedFlat });
    } catch (error) {
        console.error('Error creating Flat:', error);
        res.status(500).json({ message: 'Error creating Flat' });
    }

}

const getAllFlats = async (req, res) => {
    try {
        const flats = await Flat.find({UserId: req.user._id});
        res.status(200).json({ status: 200, message: 'Flats fetched successfully', Flats: flats });
    } catch (error) {
        console.error('Error fetching Flats:', error);
        res.status(500).json({ message: 'Error fetching Flats' });
    }
}

const getFlatById = async (req, res) => {
    try {
        const flat = await Flat.findById(req.params.id);
        if (!flat) {
            return res.status(404).json({ message: 'Flat not found' });
        }
        res.status(200).json({ status: 200, message: 'Flat fetched successfully', Flat: flat });
    } catch (error) {
        console.error('Error fetching Flat:', error);
        res.status(500).json({ message: 'Error fetching Flat' });
    }
}

const updateFlat = async (req, res) => {
    try {
        const updatedFlat = await Flat.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!updatedFlat) {
            return res.status(404).json({ message: 'Flat not found' });
        }
        res.status(200).json({ status: 200, message: 'Flat updated successfully', Flat: updatedFlat });
    } catch (error) {
        console.error('Error updating Flat:', error);
        res.status(500).json({ message: 'Error updating Flat' });
    }
}

const deleteFlat = async (req, res) => {
    try {
        const deletedFlat = await Flat.findByIdAndDelete(req.params.id);
        if (!deletedFlat) {
            return res.status(404).json({ message: 'Flat not found' });
        }
        res.status(200).json({ status: 200, message: 'Flat deleted successfully', Flat: deletedFlat });
    } catch (error) {
        console.error('Error deleting Flat:', error);
        res.status(500).json({ message: 'Error deleting Flat' });
    }
}


module.exports = { CreateFlat, getAllFlats, getFlatById, updateFlat, deleteFlat };