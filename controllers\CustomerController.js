const Customer = require('../models/Customer.model.js');

const createCustomer = async (req, res) => {
    try {
        const { CustomerName, Email, PhoneNumber, NicNumber, Address, FlatOrRoom, AssignedDate, AgreementImage, LeaveDate, IsActive } = req.body;
        if (!CustomerName) return res.status(400).json({ message: "CustomerName is required" });
        if (!PhoneNumber) return res.status(400).json({ message: "PhoneNumber is required" });
        if (PhoneNumber.length < 11)
            return res.status(400).json({ message: "PhoneNumber must be at least 11 digits" });
        if (!NicNumber) return res.status(400).json({ message: "NicNumber is required" });
        if (NicNumber.length < 13)
            return res.status(400).json({ message: "NicNumber must be at least 13 digits" });
        if (!Address) return res.status(400).json({ message: "Address is required" });
        if (!FlatOrRoom) return res.status(400).json({ message: "FlatOrRoom is required" });
        if (!AssignedDate) return res.status(400).json({ message: "AssignedDate is required" });
        const customer = new Customer({
            CustomerName,
            PhoneNumber,
            Email,
            NicNumber,
            Address,
            FlatOrRoom,
            AssignedBy: req.user._id,
            AssignedDate,
            AgreementImage,
            LeaveDate,
            IsActive
        });
        await customer.save();
        res.status(201).json({status: 201, message: 'Customer created', customer });
    } catch (error) {
        res.status(400).json({ message: 'Error creating customer', error: error.message });
    }
};

const getAllCustomers = async (req, res) => {
    try {
        const customers = await Customer.find({ AssignedBy: req.user._id })
            .populate('FlatOrRoom')
            .populate('AssignedBy');
        res.status(200).json(customers);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching customers', error: error.message });
    }
};

const getCustomerById = async (req, res) => {
    try {
        const customer = await Customer.findById(req.params.id)
            .populate('FlatOrRoom')
            .populate('AssignedBy');
        if (!customer) return res.status(404).json({ message: 'Customer not found' });
        res.status(200).json(customer);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching customer', error: error.message });
    }
};

const updateCustomer = async (req, res) => {
    try {
        const updated = await Customer.findByIdAndUpdate(
            req.params.id,
            req.body,
            { new: true }
        );
        res.status(200).json({ message: 'Customer updated', updated });
    } catch (error) {
        res.status(500).json({ message: 'Error updating customer', error: error.message });
    }
};

const deleteCustomer = async (req, res) => {
    try {
        await Customer.findByIdAndDelete(req.params.id);
        res.status(200).json({ message: 'Customer deleted' });
    } catch (error) {
        res.status(500).json({ message: 'Error deleting customer', error: error.message });
    }
};

module.exports = {
    createCustomer,
    getAllCustomers,
    getCustomerById,
    updateCustomer,
    deleteCustomer
};