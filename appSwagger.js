const swaggerJsdoc = require("swagger-jsdoc");

const swaggerOptions = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "Flat Management System API Documentation",
      version: "1.0.0",
      description:
        "Welcome to the Flat Management System API Documentation.",
    },
    servers: [
    //   {
    //     url: "https://eb-project-backend-kappa.vercel.app",
    //     description: "Production server"
    //   },
      {
        url: "http://localhost:3715",
        description: "Development server"
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT"
        }
      },
      responses: {
        UnauthorizedError: {
          description: "Access token is missing or invalid"
        },
        ForbiddenError: {
          description: "User does not have required role permissions"
        }
      },
      schemas: {
        Roles: {
          type: "string",
          enum: ["Admin", "SuperAdmin", "User"]
        }
      }
    },
    security: [{
      bearerAuth: []
    }]
  },
  apis: [
    "./routes/Bill.route.js",
    "./routes/Customer.route.js",
    "./routes/Flat.route.js",
    "./auth/Auth.route.js"
  ],
};

const swaggerSpec = swaggerJsdoc(swaggerOptions);
// console.log(JSON.stringify(swaggerSpec, null, 2));
module.exports = swaggerSpec;
