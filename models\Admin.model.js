const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const AdminSchema = new Schema({
    FullName:{
        type:String,
        required:true
    },
    Email:{
        type:String,
        required:true,
        unique:true
    },
    Password:{
        type:String,
        required:true,
        minlength:6
    },
    ConfirmPassword:{
        type:String,
        required:true,
        minlength:6,
        select:false
    },
    PhoneNumber:{
        type:Number,
        required:true,
        unique:true
    },
    Address:{
        type:String,
        required:true
    },
    ProfileImage:{
        type:String,
        required:false
    },
    OtpCode:{
        type:Number,
        required:false,
    },
    ExpiryTime:{
        type:Date,
        required:false
    },
    IsActive:{
        type:Boolean,
        required:false
    },
    Role:{
        type:String,
        required:false,
        default:"Admin",
        enum:["Admin","SuperAdmin"]
    }
},{
    timestamps: true});

module.exports =  mongoose.model('Admin', AdminSchema);