const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const Admin = require('../models/Admin.model.js')
const RegisterAdmin = async (req, res) => {
    try {
        const {
            FullName,
            Email,
            Password,
            ConfirmPassword,
            PhoneNumber,
            Address,
            ProfileImage
        } = req.body;

        const requiredFields = {
            FullName: FullName,
            Email: Email,
            Password: Password,
            ConfirmPassword: ConfirmPassword,
            PhoneNumber: PhoneNumber,
            Address: Address,
            ProfileImage: ProfileImage,
        };

        for (const [key, label] of Object.entries(requiredFields)) {
            if (!req.body[key]) {
                return res.status(400).json({ message: `Please fill in the ${label} field.` });
            }
        }

        const existingAdmin = await Admin.findOne({ Email });
        if (existingAdmin) {
            return res.status(400).json({ message: "Email already exists" });
        }

        const emailRegex = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
        if (!emailRegex.test(Email)) {
            return res.status(400).json({ message: "Invalid email address" });
        }

        if (Password.length < 6 || ConfirmPassword.length < 6) {
            return res.status(400).json({ message: "Password must be at least 6 characters long" });
        }

        if (Password !== ConfirmPassword) {
            return res.status(400).json({ message: "Passwords do not match" });
        }

        if (
            (PhoneNumber.startsWith('0') && PhoneNumber.length !== 11) ||
            (PhoneNumber.startsWith('+') && PhoneNumber.length !== 13) ||
            (!PhoneNumber.startsWith('0') && !PhoneNumber.startsWith('+'))
        ) {
            return res.status(400).json({
                message:
                    "Phone number must start with '0' (11 digits) or '+' (13 digits, e.g., +923001234567)."
            });
        }

        const hashPassword = await bcrypt.hash(Password, 10);

        const newAdmin = new Admin({
            FullName,
            Email,
            Password: hashPassword,
            ConfirmPassword,
            PhoneNumber,
            Address,
            ProfileImage
        });

        await newAdmin.save();

        return res.status(200).json({ status: 200, message: "Admin created successfully", data: newAdmin });

    } catch (error) {
        console.error("Error during admin registration:", error);
        return res.status(500).json({ message: "Internal Server Error" });
    }
};

const Login = async (req, res) => {
  try {
    const { Email, Password } = req.body;

    if (!Email) return res.status(400).json({ message: "Email is required" });
    if (!Password) return res.status(400).json({ message: "Password is required" });

    const emailRegex = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
    if (!emailRegex.test(Email)) {
      return res.status(400).json({ message: "Invalid email address format" });
    }

    if (Password.length < 6) {
      return res.status(400).json({ message: "Password must be at least 6 characters long" });
    }

    const admin = await Admin.findOne({ Email });
    if (!admin) {
      return res.status(400).json({ message: "Email is invalid" });
    }

    const isPasswordMatch = await bcrypt.compare(Password, admin.Password);
    if (!isPasswordMatch) {
      return res.status(400).json({ message: "Password is invalid" });
    }

    const token = jwt.sign(
      { id: admin._id, Email: admin.Email },
      process.env.JWT_SECRET,
      { expiresIn: "1y" }
    );

    return res.status(200).json({
      status: 200,
      message: "Admin logged in successfully",
      data: {
        id: admin._id,
        FullName: admin.FullName,
        Email: admin.Email,
        token
      }
    });

  } catch (error) {
    console.error("Error during admin login:", error);
    return res.status(500).json({ message: "Internal Server Error" });
  }
};
module.exports = { RegisterAdmin ,Login};